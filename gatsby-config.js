// support for .env, .env.development, and .env.production
require("dotenv").config()
require("dotenv").config({
  path: `.env.${process.env.NODE_ENV}`,
})

module.exports = {
  siteMetadata: {
    siteUrl: "https://www.pharmachainage.com/",
    title: "PharmaChainage",
    author: `Hazysoft`,
    description: "Trouvez l'emplacement idéal pour votre pharmacie",
  },
  plugins: [
    {
      resolve: "gatsby-source-datocms",
      options: {
        apiToken: process.env.DATOCMS_API_TOKEN,
        environment: process.env.DATOCMS_ENVIRONMENT,
      },
    },
    "gatsby-plugin-sharp",
    "gatsby-plugin-image",
    "gatsby-transformer-sharp",
    "gatsby-plugin-vanilla-extract",
    {
      resolve: "gatsby-plugin-manifest",
      options: {
        name: "PharmaChainage",
        short_name: "PharmaChainage",
        start_url: "/",
        // These can be imported once ESM support lands
        background_color: "#FDFDF6",
        theme_color: "#256D04",
        icon: "src/favicon.png",
      },
    },
    {
      resolve: `gatsby-plugin-clarity`,
      options: {
        // String value for your clarity project id
        // Project id is found in your clarity dashboard url
        // https://clarity.microsoft.com/projects/view/{clarity_project_id}/
        clarity_project_id: process.env.CLARITY_PROJECT_ID,
        // Boolean value for enabling clarity while developing
        // true will enable clarity tracking code on both development and production environments
        // false will enable clarity tracking code on production environment only
        enable_on_dev_env: false
      },
    },
    {
      resolve: `gatsby-plugin-google-gtag`,
      options: {
        // You can add multiple tracking ids and a pageview event will be fired for all of them.
        trackingIds: [
          process.env.GOOGLE_ANALYTICS_MEASUREMENT_ID // Google Analytics / GA
        ],
        // This object gets passed directly to the gtag config command
        // This config will be shared across all trackingIds
        gtagConfig: {
          anonymize_ip: true,
          // The following configurations give you the ability to disable advertising, reporting,
          // and remarketing features, and override any property settings established in the
          // Google Analytics user interface.
          // https://developers.google.com/analytics/devguides/collection/gtagjs/display-features
          allow_google_signals: false,
          cookie_expires: 0
        },
        // This object is used for configuration specific to this plugin
        pluginConfig: {
          // Puts tracking script in the head instead of the body
          head: false,
          // If you enable this optional option, Google Global Site Tag will not be loaded at all for visitors
          // that have “Do Not Track” enabled. While using Google Global Site Tag does not necessarily
          // constitute Tracking, you might still want to do this to cater to more privacy oriented users.
          respectDNT: true,
          // Avoids sending pageview hits from custom paths
          exclude: []
        }
      }
    }
  ],
}
