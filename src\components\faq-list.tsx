import * as React from "react"
import { graphql } from "gatsby"
import { <PERSON><PERSON>er, Box, Kicker, Heading, Text } from "./ui"
import * as styles from "./faq-list.css"
import { ChevronDown, ChevronUp } from "react-feather"
import { Helmet } from "react-helmet"

export interface FaqItemProps {
  id: string
  question: string
  answer: string
}

export interface FaqListProps {
  kicker?: string
  heading: string
  text?: string
  faqs: FaqItemProps[]
}

const FaqItem: React.FC<FaqItemProps> = ({ question, answer }) => {
  const [isOpen, setIsOpen] = React.useState(false)

  const toggleAccordion = () => {
    setIsOpen(!isOpen)
  }

  return (
    <div className={styles.faqItem}>
      <div
        className={styles.faqQuestion}
        onClick={toggleAccordion}
        aria-expanded={isOpen}
        role="button"
        tabIndex={0}
        onKeyPress={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            toggleAccordion()
          }
        }}
      >
        <h3 className={styles.faqQuestionText}>{question}</h3>
        <span className={styles.iconContainer}>
          {isOpen ? <ChevronUp className={styles.icon} /> : <ChevronDown className={styles.icon} />}
        </span>
      </div>
      {isOpen && <div className={styles.faqAnswer}><p>{answer}</p></div>}
    </div>
  )
}

export default function HomepageFaqList({ kicker, heading, text, faqs }: FaqListProps) {
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map((faq) => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer,
      },
    })),
  }

  return (
    <section id="faq" aria-labelledby="faq-heading">
      <Helmet>
        <script type="application/ld+json">{JSON.stringify(faqSchema)}</script>
      </Helmet>
      <Container width="narrow">
        <Box center paddingY={3}>
          <Heading id="faq-heading">
            {kicker && <Kicker>{kicker}</Kicker>}
            {heading}
          </Heading>
          {text && <Text>{text}</Text>}
        </Box>
        <div className={styles.faqList}>
          {faqs.map((faq) => (
            <FaqItem key={faq.id} {...faq} />
          ))}
        </div>
      </Container>
    </section>
  )
}

export const query = graphql`
  fragment HomepageFaqListContent on HomepageFaqList {
    id
    kicker
    heading
    text
    faqs {
      id
      question
      answer
    }
  }
`
