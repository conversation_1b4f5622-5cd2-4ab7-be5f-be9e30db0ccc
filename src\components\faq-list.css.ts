import { style, keyframes } from "@vanilla-extract/css"
import { theme } from "../theme.css"
import { media } from "./ui.css"

export const faqHeader = style({
  maxWidth: "1108px",
  marginBottom: theme.space[2],
})

export const faqList = style({
  display: "grid",
  gap: theme.space[2],
  marginTop: theme.space[2],
})

export const faqItem = style({
  borderBottom: `1px solid ${theme.colors.muted}`,
  ":last-child": {
    borderBottom: "none",
  },
  cursor: "pointer",
})

export const faqQuestion = style({
  fontSize: theme.fontSizes[3],
  fontWeight: theme.fontWeights.bold,
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  "@media": {
    [media.small]: {
      fontSize: theme.fontSizes[4],
    },
  },
})

const rotateDown = keyframes({
  from: {
    transform: 'rotate(0deg)',
  },
  to: {
    transform: 'rotate(180deg)',
  },
})

const rotateUp = keyframes({
  from: {
    transform: 'rotate(180deg)',
  },
  to: {
    transform: 'rotate(0deg)',
  },
})

export const iconContainer = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  transition: "transform 0.3s ease-in-out",
})

export const icon = style({
  animationDuration: '0.3s',
  animationFillMode: 'forwards',
})

export const iconOpen = style({
  animationName: rotateDown,
})

export const iconClose = style({
  animationName: rotateUp,
})

export const faqQuestionText = style({
  fontSize: theme.fontSizes[2],
  fontWeight: theme.fontWeights.bold,
  lineHeight: theme.lineHeights.text,
  "@media": {
    [media.small]: {
      fontSize: theme.fontSizes[3],
    },
  },
})

export const faqAnswer = style({
  fontSize: theme.fontSizes[2],
  lineHeight: theme.lineHeights.text,
  marginBottom: theme.space[4],
  "@media": {
    [media.small]: {
      fontSize: theme.fontSizes[3],
    },
  },
})
