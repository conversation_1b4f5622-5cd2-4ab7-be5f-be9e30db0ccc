import { graphql } from "gatsby"
import * as React from "react"
import {
  BlockLink,
  Container,
  Flex,
  FlexList,
  HomepageImage,
  HomepageLink,
  Link,
  Logo,
  Section,
  Space,
  Text,
  cx
} from "./ui"
import * as styles from "./ui.css"

export interface LogoItemProps {
  id: string
  alt: string
  image: HomepageImage
}

export interface LogoWithLinkItemProps {
  id: string
  image: LogoItemProps
  link: HomepageLink
}

export function LogoItem(props: LogoItemProps) {
  if (!props.image) return null

  return (
    <Logo alt={props.alt} image={props.image.gatsbyImageData} size="large" />
  )
}

export function LogoWithLinkItem(props: LogoWithLinkItemProps) {
  if (!props.image) return null

  return (
    <BlockLink {...props} to={props.link.href} target="_blank" rel="noopener noreferrer">
      <Logo alt={props.image.alt} image={props.image.image.gatsbyImageData} size="large" />
    </BlockLink>
  )
}

export interface LogoListProps {
  text?: string
  logos: LogoItemProps[],
  logoswithlinks: LogoWithLinkItemProps[]
}

export default function LogoList(props: LogoListProps) {
  return (
    <Section paddingY={4}>
      <Container width="narrow">
        {props.text && (
          <Text center variant="lead">
            {props.text}
          </Text>
        )}
        <Space size={4} />
        <FlexList gap={4} variant="center">
          {props.logoswithlinks.map(
            (logoWithLink) =>
              logoWithLink.image && (
                <li key={logoWithLink.id}>
                  <LogoWithLinkItem {...logoWithLink} />
                </li>
              )
          )}
        </FlexList>
        <Space size={4} />
        <Flex variant="center" gap={1}>
          <Text center bold variant="medium">
            {"Vous utilisez iOS ? "}
          </Text>
          <Link className={cx(styles.ctaLink, styles.blueColor, styles.text.medium)} to="ios-beta">Testez l'application en version bêta !</Link>
        </Flex>

      </Container>
    </Section>
  )
}

export const query = graphql`
  fragment HomepageLogoListContent on HomepageLogoList {
    id
    text
    logoswithlinks {
      image {
        id
        alt
        image {
          id
          gatsbyImageData
          alt
        }
      }
      link {
        id
        href
        text
      }
    }
  }
`
